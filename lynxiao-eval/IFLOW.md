# 凌霄平台 (Lynxiao Platform) 开发上下文

## 1. 项目概述

凌霄平台是一个先进的、可扩展的、事件驱动的数据资产管理与处理平台。它旨在为企业提供一站式的数据全生命周期解决方案，从异构数据源的实时接入，到通过可视化编排实现复杂的ETL、数据清洗、特征工程，再到最终将数据服务化为高性能的检索引擎或API，实现了数据从“原始”到“智能”的端到端价值转化。

**核心技术栈**:
- **语言**: Java 21 (LTS)
- **框架**: Spring Boot 3.4, Spring Cloud, Spring Data JPA, Spring WebFlux
- **构建工具**: Maven 3.8+
- **微服务治理**: 基于 Skynet Pandora 框架
- **工作流引擎**: Turing Astrolink

## 2. 核心架构与模块

平台采用分层微服务架构，主要分为以下几个层次：

### 2.1 基础数据层
- `lynxiao-platform-data`: 定义了平台所有服务间通信使用的DTOs、常量和枚举，是平台的数据契约。
- `lynxiao-platform-resource`: 定义了所有业务对象的JPA实体和Repository接口，负责与关系型数据库的交互。

### 2.2 通用组件层
- `lynxiao-platform-common`: 平台的基石，提供了如增强的Feign客户端、大字段处理（`Datashard`）、表达式工具、缓存和消息队列生产者等可复用的核心组件。

### 2.3 基础服务层
- `lynxiao-platform-region`: 轻量级的元数据中心，用于注册和发现多地域部署下的API网关地址。
- `lynxiao-platform-datashard`: 独立服务，专门用于存储和检索被`DatashardEncoder`分离出来的大文本字段。
- `lynxiao-platform-featurebase`: 封装了对Elasticsearch和MongoDB的复杂操作，提供统一的索引、检索和存储服务。

### 2.4 核心服务层
- `lynxiao-platform-dispatch`: 基于Yama框架，负责接收异步任务并将其可靠地分发给下游的`action`服务。
- `lynxiao-platform-isync`: 负责将一个数据桶的数据高效、可靠地增量同步到另一个桶。
- `lynxiao-platform-stream`: 提供实时数据流处理和数据服务API的能力。
- `lynxiao-platform-portal`: 平台级的管理门户，提供API用于管理资源和工作流。

### 2.5 业务应用层 (`lynxiao-asset`)
- `lynxiao-asset-portal`: 面向最终用户的数据资产管理门户，提供UI和API。
- `lynxiao-asset-ingest`: 负责从外部数据源（Kafka, 文件系统）实时或批量地摄取数据。
- `lynxiao-asset-action`: 一系列可独立部署的原子化微服务，执行如解析、转换、特征计算等具体任务。
- `lynxiao-asset-task`: 负责执行和管理在Portal中定义的数据流程，支持CRON定时调度和实时进度监控。

### 2.6 扩展服务层
- `lynxiao-platform-mcp`: 将平台的搜索能力通过 Anthropic MCP 协议暴露，供AI应用调用。
- `lynxiao-eval`: 专门用于分析和评估搜索系统的质量，包括好/坏案例分析、流程轨迹追踪等。
- `lynxiao-platform-test`: 用于编写和执行跨服务的端到端测试用例。

## 3. 核心技术创新：Datashard 机制

`Datashard` 是凌霄平台解决大文本字段存储性能瓶颈的核心创新。它通过将超长文本透明地分离存储到专用服务中，极大地优化了主数据库的性能。

### 3.1 DatashardEncoder (编码器)
- **功能**: 在数据写入时，自动识别并提取超过阈值（默认128字符）的文本内容，将其存储到`lynxiao-platform-datashard`服务中，并在原位置留下一个引用ID（如 `${ldoc_xxx:md5}`）。
- **路径**: `lynxiao-platform-common/src/main/java/com/iflytek/lynxiao/common/datashard/DatashardEncoder.java`
- **关键方法**:
  - `encode(String refId, String document)`: 编码单个文本。
  - `encode(List<EncodeDocumentRequest> requests)`: 批量编码多个文档，性能更优。
  - `encode(String refIdPrefix, Map<String, Object> document)`: 编码一个复杂文档对象。
- **配置**: 通过 `lynxiao.data-shard-text-size-threshold` 配置项调整阈值。

### 3.2 DatashardDecoder (解码器)
- **功能**: 在数据读取时，递归扫描文档，将引用ID替换回原始文本内容，对应用层完全透明。
- **路径**: `lynxiao-platform-common/src/main/java/com/iflytek/lynxiao/common/datashard/DatashardDecoder.java`
- **关键方法**:
  - `decode(Map<String, Object> document)`: 解码单个文档。
  - `decode(List<Map<String, Object>> documents)`: 批量解码多个文档，通过一次API调用提升效率。

该机制是平台高性能和高可扩展性的关键保障。

## 4. 关键配置

### 4.1 核心平台配置 (`lynxiao.properties`)
```properties
# 区域和环境编码
lynxiao.region-code=hf
lynxiao.env-code=prod

# Datashard 阈值
lynxiao.data-shard-text-size-threshold=128

# 关键服务地址
lynxiao.portal-service-url=http://tlb:lynxiao-portal
lynxiao.datashard-service-url=http://tlb:lynxiao-datashard
```

### 4.2 数据库配置
平台使用多数据源策略：
- **MySQL**: 存储元数据（用户、权限、任务定义等）。
- **MongoDB**: 存储文档型数据（原始数据桶、处理后数据集等）。
- **Elasticsearch**: 提供全文检索和向量搜索能力。

### 4.3 构建配置 (`pom.xml`)
主 `pom.xml` 文件定义了项目的模块结构和依赖管理，确保所有子模块使用统一的依赖版本（如 `turing-astrolink`, `elasticsearch-java`, `redisson` 等）。

## 5. 如何开始开发

1.  **环境准备**: 确保已安装 JDK 21, Maven 3.8+, 并启动依赖的中间件（MySQL, MongoDB, ES, Kafka, Redis）。
2.  **配置服务**: 根据 `README.md` 和 `lynxiao.properties` 模板，配置正确的数据库连接和服务地址。
3.  **启动顺序**: 优先启动基础服务（`region`, `datashard`），然后是核心服务（`featurebase`, `dispatch`），最后是业务服务（`asset-portal`, `asset-ingest`）。
4.  **验证**: 通过访问 `http://localhost:8080/actuator/health` 或调用核心API（如 `/featurebase/api/v1/es/info`）来验证服务是否正常运行。