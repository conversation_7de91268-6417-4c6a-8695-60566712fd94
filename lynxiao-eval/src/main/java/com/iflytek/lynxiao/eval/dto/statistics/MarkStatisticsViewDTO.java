package com.iflytek.lynxiao.eval.dto.statistics;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.annotation.JSONField;
import com.iflytek.lynxiao.common.feign.LynxiaoFeignClientManager;
import com.iflytek.lynxiao.eval.component.domain.EvalResultItem;
import com.iflytek.lynxiao.eval.domain.DimsGroupDetailLevel;
import com.iflytek.lynxiao.eval.domain.mark.result.*;
import com.iflytek.lynxiao.eval.entity.MarkResultEntity;
import com.iflytek.lynxiao.eval.service.statistics.impl.MarkStatisticsServiceImpl;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import skynet.boot.pandora.api.Jsonable;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 标注结果展示列表项
 * <p>
 * 将标注结果按照固定列和动态列展示。
 * 固定列包括：策略名称、环境、标注人、query、标注时间、来源、文档位置、文档url、文档标题等，
 * 动态列包括：标注结果中标注维度、标注反馈、归因分析反馈、大模型推理反馈、全链路点赞点踩说明等。
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class MarkStatisticsViewDTO extends Jsonable {

    private static final String EXTEND_FIELD_DIM = "dim";
    private static final String EXTEND_FIELD_FEEDBACK = "feedback";
    private static final String EXTEND_FIELD_TRACE = "trace";
    private static final String EXTEND_FIELD_CHAT = "chat";

    private static final String TABLE_HEADER_DOC_IGNORE = "doc类弃标";
    private static final String TABLE_HEADER_QUERY_IGNORE = "query类弃标";

    private static final String EXTEND_FIELD_ASCRIBE_GOOD = "ascribe_good";
    private static final String EXTEND_FIELD_ASCRIBE_BAD = "ascribe_bad";

    private static final String EXTEND_FIELD_ASCRIBE_ERR_MSG = "ascribe_err_msg";

    /**
     * traceId
     */
    private String traceId;

    /**
     * 标注人账号
     */
    private String account;

    /**
     * 测评记录id
     */
    private String markRecordId;

    /**
     * 场景策略名称
     */
    private String sceneProcessName;

    /**
     * 场景id
     */
    private String sceneProcessId;

    /**
     * 归因模式
     *
     * @see com.iflytek.lynxiao.eval.domain.AscribeMode
     */
    private int ascribeMode;

    /**
     * 归因策略名称
     */
    private String ascribeProcessName;

    /**
     * 竞对场景策略名称
     */
    private String compareProcessName;

    /**
     * 策略配置id
     */
    private String strategyId;

    /**
     * 区域
     */
    private String regionCode;

    /**
     * 区域名称
     */
    private String regionName;

    /**
     * 问题
     */
    private String query;

    /**
     * 标注人
     */
    private String createdBy;

    /**
     * 标注时间- 使用最近修改时间
     */
    private Instant lastModifiedDate;

    /**
     * 标注目标类型
     * 0：策略结果（召回的文档）
     * 1：全链路结果
     * 2：大模型结果
     */
    private Integer type;

    /**
     * type对应中文名称
     */
    private String typeName;

    /**
     * doc位置
     */
    private Integer docIdx;

    /**
     * 文档ID
     */
    private String docId;

    /**
     * 文档url
     */
    private String url;

    /**
     * 文档标题
     */
    private String title;

    /**
     * 标注记录id
     */
    private String resultId;

    /**
     * 标注元数据
     */
    private JSONObject metadata;

    /**
     * 全链路类型
     */
    private String traceView;

    /**
     * 动态扩展字段
     */
    private LinkedHashMap<String, Object> extendFieldMap;

    @JSONField(serialize = false)
    private List<ExtendField> extendFieldList = new ArrayList<>();

    private String getExtendFieldValue(String fieldName) {
        for (ExtendField extendField : this.extendFieldList) {
            if (extendField.getName().equals(fieldName)) {
                return extendField.getValue();
            }
        }
        return null;
    }

    /**
     * 将标注结果列表转换为MarkStatisticsViewDTO 统计结果列表
     *
     * @param markResultEntities 标注结果列表
     * @param regionMap          区域映射
     * @param strategyIdNameMap  策略配置ID到策略配置名称映射
     * @return
     */
    public static List<MarkStatisticsViewDTO> of(List<MarkResultEntity> markResultEntities, Map<String, LynxiaoFeignClientManager.MetaRegionFeign.MetaRegion> regionMap,
                                                 Map<String, MarkStatisticsServiceImpl.StrategyIdNameDTO> strategyIdNameMap,
                                                 Map<String, String> withoutStrategyIdNameMap) {
        List<MarkStatisticsViewDTO> resultDetailList = markResultEntities.stream()
                .map(t -> convert2DTO(t, regionMap, strategyIdNameMap, withoutStrategyIdNameMap))
                .collect(Collectors.toList());

        // 合并动态表头,并按照指定的列顺序返回
        processExtendFieldMap(resultDetailList);

        return resultDetailList;
    }


    /**
     * 将单条标注结果转换为MarkStatisticsViewDTO
     *
     * @param markResultEntity  标注结果
     * @param regionCodeMap     区域编码映射
     * @param strategyIdNameMap 策略ID到名称的映射
     * @return
     */
    private static MarkStatisticsViewDTO convert2DTO(MarkResultEntity markResultEntity, Map<String, LynxiaoFeignClientManager.MetaRegionFeign.MetaRegion> regionCodeMap,
                                                     Map<String, MarkStatisticsServiceImpl.StrategyIdNameDTO> strategyIdNameMap,
                                                     Map<String, String> withoutStrategyIdNameMap) {
        // 1. 获取基本信息
        MarkStatisticsViewDTO markStatisticsViewDTO = BeanUtil.copyProperties(markResultEntity, MarkStatisticsViewDTO.class);
        LynxiaoFeignClientManager.MetaRegionFeign.MetaRegion metaRegion = regionCodeMap.get(markResultEntity.getRegionCode());
        markStatisticsViewDTO.setRegionName(null == metaRegion ? "" : metaRegion.getName());
        markStatisticsViewDTO.setSceneProcessName(StringUtils.isBlank(markResultEntity.getStrategyId()) ?
                withoutStrategyIdNameMap.get(markResultEntity.getSceneProcessId()) :
                strategyIdNameMap.get(markResultEntity.getStrategyId()).getRecallProcessName());
        markStatisticsViewDTO.setTypeName(MarkResultType.getName(markResultEntity.getType()));
        markStatisticsViewDTO.setDocIdx(markResultEntity.getDocIdx());
        markStatisticsViewDTO.setTraceId(markResultEntity.getTraceId());
        markStatisticsViewDTO.setUrl(markResultEntity.getUrl());
        markStatisticsViewDTO.setDocId(markResultEntity.getDocId());
        markStatisticsViewDTO.setResultId(markResultEntity.getId());
        markStatisticsViewDTO.setMetadata(markResultEntity.getMetadata());
        markStatisticsViewDTO.setStrategyId(markResultEntity.getStrategyId());
        markStatisticsViewDTO.setAscribeMode(markResultEntity.getAscribeMode());
        markStatisticsViewDTO.setAscribeProcessName(StringUtils.isBlank(markResultEntity.getStrategyId()) ?
                StringUtils.EMPTY :
                strategyIdNameMap.get(markResultEntity.getStrategyId()).getEvalStrategyName());
        markStatisticsViewDTO.setCompareProcessName(StringUtils.isBlank(markResultEntity.getStrategyId()) ?
                StringUtils.EMPTY :
                strategyIdNameMap.get(markResultEntity.getStrategyId()).getCompareProcessName());
        if (Objects.equals(markResultEntity.getType(), MarkResultType.TRACE.getCode())) {
            markStatisticsViewDTO.setTraceView(markResultEntity.getData().getTrace().getTraceView());
        }


        // 2. 分别解析不同标注对象，得到解析后的结果  注：无结果的标注记录不需要解析扩展字段
        if (!Objects.equals(markResultEntity.getType(), MarkResultType.NO_RESULT.getCode())) {
            markStatisticsViewDTO.setExtendFieldList(ExtendField.from(markResultEntity));
        }

        return markStatisticsViewDTO;
    }

    /**
     * 处理标注结果列表，补全extendMap中的key, 并按顺序排列对齐列
     */
    private static void processExtendFieldMap(List<MarkStatisticsViewDTO> markResultList) {
        // 初始化三个集合分别存储不同类型的key
        //一级标注维度 dim
        Set<String> firstSectionKeys = new LinkedHashSet<>();
        //二级标注维度（内容质量问题详情） feedback
        Set<String> secondSectionKeys = new LinkedHashSet<>();

        //三级标注维度（内容质量问题详情） trace
        Set<String> thirdSectionKeys = new LinkedHashSet<>();

        //四级标注维度（全链路标注内容） chat
        Set<String> fourSectionKeys = new LinkedHashSet<>();

        //五级标注维度（good 归因）
        Set<String> fiveSectionKeys = new LinkedHashSet<>();

        //六级标注维度（bad 归因）
        Set<String> fixSectionKeys = new LinkedHashSet<>();

        //七级标注维度（归因错误原因）
        Set<String> sevenSectionKeys = new LinkedHashSet<>();

        for (MarkStatisticsViewDTO markStatisticsViewDTO : markResultList) {
            for (ExtendField extendField : markStatisticsViewDTO.getExtendFieldList()) {
                // 处理标注维度
                if (EXTEND_FIELD_DIM.equals(extendField.getType())) {
                    firstSectionKeys.add(extendField.getName());
                }
                // 处理内容质量问题
                else if (EXTEND_FIELD_FEEDBACK.equals(extendField.getType())) {
                    secondSectionKeys.add(extendField.getName());
                } else if (EXTEND_FIELD_TRACE.equals(extendField.getType())) {
                    thirdSectionKeys.add(extendField.getName());
                } else if (EXTEND_FIELD_CHAT.equals(extendField.getType())) {
                    fourSectionKeys.add(extendField.getName());
                } else if (EXTEND_FIELD_ASCRIBE_GOOD.equals(extendField.getType())) {
                    fiveSectionKeys.add(extendField.getName());
                } else if (EXTEND_FIELD_ASCRIBE_BAD.equals(extendField.getType())) {
                    fixSectionKeys.add(extendField.getName());
                } else if (EXTEND_FIELD_ASCRIBE_ERR_MSG.equals(extendField.getType())) {
                    sevenSectionKeys.add(extendField.getName());
                }
            }
        }

        //对第二个集合进行排序，确保doc类弃标与doc类弃标备注在一起  query类弃标与query类弃标备注在一起
        secondSectionKeys = secondSectionKeys.stream()
                .sorted(Comparator.comparing(s -> s.contains(TABLE_HEADER_DOC_IGNORE) ? 0 : (s.contains(TABLE_HEADER_QUERY_IGNORE) ? 1 : 2)))
                .collect(Collectors.toCollection(LinkedHashSet::new));


        // 合并三个集合得到最终的有序key集合
        LinkedHashSet<String> allKeys = new LinkedHashSet<>();
        allKeys.addAll(firstSectionKeys);
        allKeys.addAll(secondSectionKeys);
        allKeys.addAll(thirdSectionKeys);
        allKeys.addAll(fourSectionKeys);
        allKeys.addAll(fiveSectionKeys);
        allKeys.addAll(fixSectionKeys);
        allKeys.addAll(sevenSectionKeys);

        // 遍历每个MarkResultListDTO对象，补全extendMap中的key
        for (MarkStatisticsViewDTO dto : markResultList) {
            LinkedHashMap<String, Object> extendMap = new LinkedHashMap<>();
            for (String key : allKeys) {
                extendMap.put(key, dto.getExtendFieldValue(key));
            }
            dto.setExtendFieldMap(extendMap);
        }
    }


    /**
     * 标注结果动态列
     */
    @Setter
    @Getter
    @AllArgsConstructor
    public static class ExtendField {

        /**
         * 字段类型， 如 EXTEND_FIELD_DIM，用于视图中排序
         */
        private String type;

        /**
         * 列名，如 相关度
         */
        private String name;

        /**
         * 列值，如 完全回答
         */
        private String value;


        /**
         * 将标注结果转成动态列
         *
         * @param markResultEntity 标注结果实体
         * @return 动态列
         */
        public static List<ExtendField> from(MarkResultEntity markResultEntity) {
            assert markResultEntity != null;
            MarkResultData data = markResultEntity.getData();
            assert data != null;

            MarkResultRecall recallResult = data.getRecall();
            MarkResultTrace traceResult = data.getTrace();
            MarkResultChat chatResult = data.getChat();
            MarkResultQueryIgnore queryIgnoreResult = data.getQueryIgnore();

            List<ExtendField> extendFieldList = new ArrayList<>();

            // 解析最终标注结果
            if (null != recallResult) {
                if (recallResult.getDimsList() != null) {
                    for (MarkResultDims markResultDim : recallResult.getDimsList()) {
                        // 维度和选项，当前只支持选项单选（好，或者不好）

                        List<MarkResultDims> list = markResultDim.getChildren().stream().filter(item -> item.getLevel() == DimsGroupDetailLevel.OPTION && item.isValue()).toList();
                        String markOptions = StringUtils.join(list.stream().map(MarkResultDims::getName).collect(Collectors.toList()), ",");

                        //todo 考虑反馈 与反馈细则如何表现
                        extendFieldList.add(new ExtendField(recallResult.isIgnore() ? EXTEND_FIELD_FEEDBACK : EXTEND_FIELD_DIM, recallResult.isIgnore() ? TABLE_HEADER_DOC_IGNORE : markResultDim.getName(), markOptions));
                    }

                    //此为doc类弃标备注
                    if (recallResult.isIgnore()) {
                        extendFieldList.add(new ExtendField(EXTEND_FIELD_FEEDBACK, TABLE_HEADER_DOC_IGNORE + "备注", recallResult.getRemark()));
                    }

                }
                // 解析归因分析, key: 重排-V2，value: 重排过滤
                String fieldType = markResultEntity.getAscribeMode() == 0 ? EXTEND_FIELD_ASCRIBE_GOOD : EXTEND_FIELD_ASCRIBE_BAD;
                if (recallResult.getAscribe() != null) {
                    MarkResultRecallAscribe ascribe = recallResult.getAscribe();
                    if (ascribe.getAscribeList() != null) {
                        for (int i = 1; i < ascribe.getAscribeList().size() + 1; i++) {
                            EvalResultItem evalResultItem = ascribe.getAscribeList().get(i - 1);
                            extendFieldList.add(new ExtendField(fieldType, evalResultItem.getName(), String.valueOf(evalResultItem.getValue())));
                        }
                    }

                    if (StringUtils.isNotBlank(ascribe.getErrMsg())) {
                        extendFieldList.add(new ExtendField(EXTEND_FIELD_ASCRIBE_ERR_MSG, "归因错误信息", ascribe.getErrMsg()));
                    }
                }
            }

            //解析query类弃标标注结果
            if (queryIgnoreResult != null) {
                if (queryIgnoreResult.getIgnore() != null) {
                    for (MarkResultDims markResultDim : queryIgnoreResult.getIgnore()) {
                        // 维度和选项，当前只支持选项单选（好，或者不好）

                        List<MarkResultDims> list = markResultDim.getChildren().stream().filter(item -> item.getLevel() == DimsGroupDetailLevel.OPTION && item.isValue()).toList();
                        String markOptions = StringUtils.join(list.stream().map(MarkResultDims::getName).collect(Collectors.toList()), ",");

                        //todo 考虑反馈 与反馈细则如何表现
                        extendFieldList.add(new ExtendField(EXTEND_FIELD_FEEDBACK, TABLE_HEADER_QUERY_IGNORE, markOptions));
                    }
                }

                // 备注加到归因分析列中展示
                extendFieldList.add(new ExtendField(EXTEND_FIELD_FEEDBACK, TABLE_HEADER_QUERY_IGNORE + "备注", queryIgnoreResult.getRemark()));
            }

            // 全链路分析， key: 踩，value: 不该召回到
            if (traceResult != null) {
                extendFieldList.add(new ExtendField(EXTEND_FIELD_TRACE, "全链路标注", "0".equals(traceResult.getValue()) ? "赞" : "踩"));
                extendFieldList.add(new ExtendField(EXTEND_FIELD_TRACE, "全链路标注备注", traceResult.getRemark()));
            }
            // chat分析, key: 满意回答， value: -
            if (chatResult != null && null != chatResult.getFeedbackList()) {
                for (int i = 1; i < chatResult.getFeedbackList().size() + 1; i++) {

                    MarkResultDims markResultDims = chatResult.getFeedbackList().get(i - 1);

                    extendFieldList.add(new ExtendField(EXTEND_FIELD_CHAT, "chat标注" + i, markResultDims.getName()));
                    List<String> chatMarkValue = new ArrayList<>();
                    try {
                        chatMarkValue = markResultDims.getChildren().stream().filter(MarkResultDims::isValue).map(MarkResultDims::getName).toList();
                    } catch (Exception e) {
                        // 可能是因为没有子项，直接忽略
                    }
                    extendFieldList.add(new ExtendField(EXTEND_FIELD_CHAT, "chat标注备注" + i, StringUtils.join(chatMarkValue, ",")));
                }
            }
            return extendFieldList;
        }
    }
}
