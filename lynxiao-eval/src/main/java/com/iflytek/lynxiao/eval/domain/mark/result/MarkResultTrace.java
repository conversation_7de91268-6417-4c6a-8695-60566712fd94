package com.iflytek.lynxiao.eval.domain.mark.result;

import lombok.Getter;
import lombok.Setter;
import skynet.boot.pandora.api.Jsonable;

/**
 * 标注结果-全链路标注结果
 */
@Setter
@Getter
public class MarkResultTrace extends Jsonable {

    /**
     * 是否是mock结果的标注（goodcase分析场景会存在召回的文档是mock）
     */
    private boolean isMock;

    /**
     * 用于结果和标注对象之间的定位：在全链路中的组件列表中的位次
     */
    private int compIdx;

    /**
     * 用于结果和标注对象之间的定位：组件召回文档列表中的位次
     */
    private int docIdx;

    /**
     * 标明是什么全链路类型产生的标注， 召回全链路 recall ，归因全链路 ascribe
     */
    private String traceView;

    /**
     * 组件名称
     */
    private String nodeName;

    /**
     * 节点id
     */
    private String nodeId;

    /**
     * 反馈问题类型， 0： 赞  1 ：踩
     */
    private String value;

    /**
     * 反馈备注
     */
    private String remark;

}
