package com.iflytek.lynxiao.eval.dto.mark.record;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.eval.domain.TraceViewType;
import com.iflytek.lynxiao.eval.domain.mark.MarkTargetData;
import com.iflytek.lynxiao.eval.domain.mark.MarkTargetTrace;
import com.iflytek.lynxiao.eval.domain.mark.result.MarkResultTrace;
import com.iflytek.lynxiao.eval.dto.mark.result.MarkResultTraceDTO;
import com.iflytek.lynxiao.eval.entity.MarkMockEntity;
import com.iflytek.lynxiao.eval.entity.MarkResultEntity;
import com.iflytek.lynxiao.eval.entity.MarkTargetEntity;
import lombok.Getter;
import lombok.Setter;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.CollectionUtils;
import skynet.boot.pandora.api.Jsonable;

import java.util.ArrayList;
import java.util.List;

/**
 * 用于存储全链路中单个节点值、以及标注值
 */
@Setter
@Getter
public class TraceRecordDTO extends Jsonable {

    /**
     * 组件code
     */
    private String code;

    /**
     * 组件名称
     */
    private String name;

    /**
     * 节点id
     */
    private String nodeId;

    /**
     * 位次字段
     */
    private String indexField;

    /**
     * 得分字段
     */
    private String scoreField;

    //todo 待考虑非召回、排序节点全链路展示

    /**
     * 文档与标注结果列表
     */
    private List<TraceDocRecordDTO> docs;

    /**
     * 组件正常返回的文档数量
     */
    private Integer docSize;

    /**
     * 文档与标注结果列表 - mock
     */
    private List<TraceDocRecordDTO> mockDocs;

    /**
     * 构建全链路标注结果 target + result
     *
     * @param markResultEntities 全链路标注结果列表
     * @param markTargetEntity   测评对象
     */
    public static List<TraceRecordDTO> of(List<MarkResultEntity> markResultEntities, MarkTargetEntity markTargetEntity,
                                          List<MarkMockEntity> markMockEntities, String traceViewType) {

        if (markTargetEntity.getData() == null) {
            return new ArrayList<>(0);
        }

        List<MarkTargetTrace> targetTraces = getTraces(markTargetEntity, traceViewType);
        List<TraceRecordDTO> response = convertEntity2Dto(targetTraces, markMockEntities);

        if (CollectionUtils.isEmpty(markResultEntities)) {
            //如果没有标注结果，直接返回
            return response;
        }

        //将标注结果根据横纵坐标（compIdx、 docIdx）绑定到dto上
        for (MarkResultEntity resultEntity : markResultEntities) {

            if (resultEntity.getData() == null || resultEntity.getData().getTrace() == null) {
                continue;
            }

            MarkResultTrace resultTrace = resultEntity.getData().getTrace();
            TraceRecordDTO traceRecordDTO = response.get(resultTrace.getCompIdx());

            TraceDocRecordDTO traceDocRecordDTO = resultTrace.isMock() ?
                    traceRecordDTO.getMockDocs().get(resultTrace.getDocIdx()) :
                    traceRecordDTO.getDocs().get(resultTrace.getDocIdx());

            MarkResultTraceDTO markResultTraceDTO = BeanUtil.copyProperties(resultTrace, MarkResultTraceDTO.class);
            markResultTraceDTO.setResultId(resultEntity.getId());
            traceDocRecordDTO.setMarkResult(markResultTraceDTO);
        }

        return response;
    }

    /**
     * 选择使用哪种全链路列表
     */
    private static List<MarkTargetTrace> getTraces(MarkTargetEntity markTargetEntity, String traceViewType) {

        MarkTargetData data = markTargetEntity.getData();

        if (TraceViewType.ASCRIBE.equals(traceViewType)) {
            //归因分析全链路
            return data.getAscribeTraceList();
        } else {
            //普通视图
            return data.getTraceList();
        }
    }


    /**
     * 将数据库存储对象转为dto
     *
     * @param traceList 数据库中的全链路列表
     */
    @NotNull
    private static List<TraceRecordDTO> convertEntity2Dto(List<MarkTargetTrace> traceList, List<MarkMockEntity> markMockEntities) {
        List<TraceRecordDTO> response = new ArrayList<>();

        //MarkTargetTrace 转为 TraceRecordDTO
        for (MarkTargetTrace targetTrace : traceList) {
            TraceRecordDTO traceRecordDTO = BeanUtil.copyProperties(targetTrace, TraceRecordDTO.class);

            //处理doc列表
            List<TraceDocRecordDTO> docs = new ArrayList<>();
            for (JSONObject doc : targetTrace.getDocs()) {
                TraceDocRecordDTO traceDocRecordDTO = new TraceDocRecordDTO();
                traceDocRecordDTO.setDoc(doc);
                docs.add(traceDocRecordDTO);
            }

            //处理mockDoc列表
            markMockEntities.stream().filter(item -> targetTrace.getNodeId().equals(item.getNodeId()) && targetTrace.getCode().equals(item.getCode()))
                    .findFirst()
                    .ifPresent(item -> traceRecordDTO.setMockDocs(TraceDocRecordDTO.of(item.getData().getDocs())));

            traceRecordDTO.setDocs(docs);
            response.add(traceRecordDTO);
        }
        return response;
    }
}
