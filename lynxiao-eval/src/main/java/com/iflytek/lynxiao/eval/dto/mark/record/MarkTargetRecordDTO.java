package com.iflytek.lynxiao.eval.dto.mark.record;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.eval.domain.AscribeMode;
import com.iflytek.lynxiao.eval.domain.TraceViewType;
import com.iflytek.lynxiao.eval.domain.mark.result.MarkResultType;
import com.iflytek.lynxiao.eval.entity.MarkMockEntity;
import com.iflytek.lynxiao.eval.entity.MarkResultEntity;
import com.iflytek.lynxiao.eval.entity.MarkTargetEntity;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import skynet.boot.pandora.api.Jsonable;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 单个场景（场景策略或者产品方案）的测评记录 ， 包含最终召回、全链路、chat的测评对象 + 标注结果
 */
@Setter
@Getter
public class MarkTargetRecordDTO extends Jsonable {

    /**
     * 测评对象id
     */
    private String targetId;

    /**
     * traceId
     */
    private String traceId;

    /**
     * 测评目录中的 策略配置id
     */
    private String strategyId;

    /**
     * 流程id
     */
    private String processId;

    /**
     * 用于获取全链路时区分召回全链路与归因全链路
     *
     * @see TraceViewType
     */
    private String traceView;

    /**
     * 元数据
     */
    private JSONObject metadata;

    /**
     * 流程最终召回结果标注记录
     */
    private List<RecallRecordDTO> recallList;

    /**
     * 流程执行全链路列表标注记录
     */
    private List<TraceRecordDTO> traceList;

    /**
     * 大模型推理结果以及标注结果
     */
    private ChatRecordDTO chat;

    public static MarkTargetRecordDTO of(String traceId, String strategyId, List<RecallRecordDTO> recallList, List<TraceRecordDTO> traceList, ChatRecordDTO chat) {
        MarkTargetRecordDTO markTargetRecordDTO = new MarkTargetRecordDTO();
        markTargetRecordDTO.setTraceId(traceId);
        markTargetRecordDTO.setStrategyId(strategyId);
        markTargetRecordDTO.setRecallList(recallList);
        markTargetRecordDTO.setTraceList(traceList);
        markTargetRecordDTO.setChat(chat);

        return markTargetRecordDTO;
    }

    public static MarkTargetRecordDTO of(MarkTargetEntity markTargetEntity, List<MarkResultEntity> markResultEntities,
                                         List<MarkMockEntity> markMockEntities, MarkRecordQueryDTO queryDTO) {
        assert markTargetEntity != null;

        // 匹配target 到对应标注结果记录
        List<MarkResultEntity> resultEntities = markResultEntities.stream().filter(item -> item.getMarkTargetId().equals(markTargetEntity.getId())).toList();

        MarkTargetRecordDTO markTargetRecordDTO = new MarkTargetRecordDTO();
        markTargetRecordDTO.setTargetId(markTargetEntity.getId());
        markTargetRecordDTO.setTraceId(markTargetEntity.getTraceId());
        markTargetRecordDTO.setStrategyId(markTargetEntity.getStrategyId());
        markTargetRecordDTO.setProcessId(markTargetEntity.getProcessId());
        markTargetRecordDTO.setMetadata(markTargetEntity.getMetadata());

        if (queryDTO.isRecall()) {
            markTargetRecordDTO.setRecallList(RecallRecordDTO.of(resultEntities, markTargetEntity));
        }

        if (queryDTO.isTrace()) {

            List<MarkMockEntity> filterMockEntities = new ArrayList<>();
            if (StringUtils.isNotBlank(queryDTO.getDocId()) || StringUtils.isNotBlank(queryDTO.getDocUrl())) {
                // 如果指定了docId或者docUrl，则需要传入mock数据
                filterMockEntities = markMockEntities.stream().filter(item -> {
                    if (StringUtils.isNotBlank(queryDTO.getDocId())) {
                        return queryDTO.getDocId().equals(item.getDocId());
                    }
                    return true;
                }).filter(item -> {
                    if (StringUtils.isNotBlank(queryDTO.getDocUrl())) {
                        return queryDTO.getDocUrl().equals(item.getUrl());
                    }
                    return true;
                }).toList();
            }
            //选择归因全链路类型
            String traceViewType = choiceTraceList(markTargetEntity, queryDTO);
            markTargetRecordDTO.setTraceList(TraceRecordDTO.of(resultEntities, markTargetEntity, filterMockEntities, traceViewType));
            markTargetRecordDTO.setTraceView(traceViewType);
        }

        if (queryDTO.isChat()) {
            Optional<MarkResultEntity> chatResultOp = resultEntities.stream().filter(item -> Objects.equals(item.getType(), MarkResultType.CHAT.getCode())).findFirst();
            markTargetRecordDTO.setChat(ChatRecordDTO.of(chatResultOp.orElse(null), markTargetEntity));
        }

        return markTargetRecordDTO;
    }

    /**
     * 选择使用哪种全链路列表
     */
    private static String choiceTraceList(MarkTargetEntity markTargetEntity, MarkRecordQueryDTO queryDTO) {
        if (markTargetEntity.getAscribeMode() == AscribeMode.UNKNOWN) {
            return StringUtils.EMPTY;
        }

        if (markTargetEntity.getAscribeMode() == AscribeMode.BAD) {
            //badCase归因分析场景下，始终使用结果召回全链路
            return TraceViewType.RECALL;
        }

        return queryDTO.getTraceView();
    }
}
