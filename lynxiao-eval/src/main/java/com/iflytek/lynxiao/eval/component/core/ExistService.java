package com.iflytek.lynxiao.eval.component.core;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.lynxiao.common.feign.asset.AssetPortalApi;
import com.iflytek.lynxiao.common.feign.region.DomainBlackApi;
import com.iflytek.lynxiao.data.domain.AssetAuditStatus;
import com.iflytek.lynxiao.data.domain.AssetCell;
import com.iflytek.lynxiao.data.domain.AssetCellProps;
import com.iflytek.lynxiao.data.domain.AssetOperationType;
import com.iflytek.lynxiao.data.dto.asset.AssetBucketDTO;
import com.iflytek.lynxiao.data.dto.asset.AssetKindCode;
import com.iflytek.lynxiao.data.dto.asset.CellQueryDTO;
import com.iflytek.lynxiao.data.dto.asset.IdxBucketExtConfig;
import com.iflytek.lynxiao.eval.component.domain.ExistCheckResult;
import com.iflytek.lynxiao.eval.component.domain.PreEvalDoc;
import com.iflytek.lynxiao.eval.component.domain.TraceLogItem;
import com.iflytek.lynxiao.eval.utils.WorkflowUtil;
import com.iflytek.turing.astrolink.service.dto.WorkflowProcess;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import skynet.boot.pandora.exception.PandoraException;

import java.util.*;

/**
 * 分析文档Url是否存在数据桶中
 *
 * <AUTHOR>  2025/3/6 09:12
 */
@Slf4j
@Service
public class ExistService {

    private final UrlDocConverter urlDocConverter;
    private final AssetPortalApi assetPortalApi;
    private final DomainBlackApi domainBlackApi;

    public ExistService(UrlDocConverter urlDocConverter, AssetPortalApi assetPortalApi, DomainBlackApi domainBlackApi) {
        this.urlDocConverter = urlDocConverter;
        this.assetPortalApi = assetPortalApi;
        this.domainBlackApi = domainBlackApi;
    }

    /**
     * 资产单元状态分类器
     * 用于对AssetCell进行状态分类，减少重复代码
     */
    private static class AssetCellClassifier {
        private final List<AssetCell> foundCells = new ArrayList<>();
        private final List<AssetCell> domainBlackCells = new ArrayList<>();
        private final List<AssetCell> rejectCells = new ArrayList<>();
        private final List<AssetCell> unreviewedCells = new ArrayList<>();
        private final List<AssetCell> deletedCells = new ArrayList<>();
        private final List<AssetCell> deDupedCells = new ArrayList<>();

        public void addCell(AssetCell cell, AssetBucketDTO bucket, ExistService service) {
            foundCells.add(cell);

            if (service.isInBlackList(bucket, cell)) {
                domainBlackCells.add(cell);
            }
            if (service.isAssetCellRejected(cell)) {
                rejectCells.add(cell);
            }
            if (service.isAssetCellUnreviewed(cell)) {
                unreviewedCells.add(cell);
            }
            if (service.isAssetCellDeleted(cell)) {
                deletedCells.add(cell);
            }
            if (service.isAssetCellDeDuped(cell)) {
                deDupedCells.add(cell);
            }
        }

        public List<AssetCell> getFoundCells() {
            return foundCells;
        }

        public List<AssetCell> getDomainBlackCells() {
            return domainBlackCells;
        }

        public List<AssetCell> getRejectCells() {
            return rejectCells;
        }

        public List<AssetCell> getUnreviewedCells() {
            return unreviewedCells;
        }

        public List<AssetCell> getDeletedCells() {
            return deletedCells;
        }

        public List<AssetCell> getDeDupedCells() {
            return deDupedCells;
        }

        public boolean isEmpty() {
            return foundCells.isEmpty();
        }

        /**
         * 检查是否所有找到的单元都符合指定条件
         */
        public boolean allMatch(List<AssetCell> targetCells) {
            return !foundCells.isEmpty() && foundCells.size() == targetCells.size();
        }

        /**
         * 获取状态正常的单元（排除所有异常状态）
         */
        public List<AssetCell> getStatusOkCells() {
            return foundCells.stream()
                    .filter(cell -> !rejectCells.contains(cell)
                            && !domainBlackCells.contains(cell)
                            && !unreviewedCells.contains(cell)
                            && !deletedCells.contains(cell)
                            && !deDupedCells.contains(cell))
                    .toList();
        }
    }

    /**
     * 检查状态并设置结果
     */
    private boolean checkStatusAndSetResult(ExistCheckResult existCheckResult, GoodCaseContext goodCaseContext,
                                            AssetCellClassifier classifier, String logPrefix, boolean checkBlacklist, boolean checkDedup) {

        // 检查黑名单（仅索引库需要）
        if (checkBlacklist && classifier.allMatch(classifier.getDomainBlackCells())) {
            existCheckResult.setReason4Disabled();
            existCheckResult.setEnd(true);
            return true;
        }

        // 检查审核拒绝
        if (classifier.allMatch(classifier.getRejectCells())) {
            existCheckResult.setReason4Rejected();
            existCheckResult.setEnd(true);
            return true;
        }

        // 检查待审核
        if (classifier.allMatch(classifier.getUnreviewedCells())) {
            existCheckResult.setReason4Unreviewed();
            existCheckResult.setEnd(true);
            return true;
        }

        // 检查删除
        if (classifier.allMatch(classifier.getDeletedCells())) {
            existCheckResult.setReason4Deleted();
            existCheckResult.setEnd(true);
            return true;
        }

        // 检查去重（仅数据集需要）
        if (checkDedup && classifier.allMatch(classifier.getDeDupedCells())) {
            existCheckResult.setReason4Dup();
            existCheckResult.setEnd(true);
            return true;
        }

        // 检查状态正常的单元
        List<AssetCell> statusOkCells = classifier.getStatusOkCells();
        if (!statusOkCells.isEmpty()) {
            log.info("doc在{}中存在状态正常记录, id:{}, url:{}", logPrefix,
                    goodCaseContext.getInput().getGoodId(), goodCaseContext.getInput().getGoodUrl());

            if (logPrefix.equals("数据集")){
                existCheckResult.setReason4Exception(logPrefix);
            }

            existCheckResult.setEnd(true);
            return true;
        } else {
            // 其他状态（部分被各种原因过滤）
            existCheckResult.setReason4Useless(logPrefix);
            log.error("doc在{}中被多种原因过滤掉, id:{}, url:{}", logPrefix,
                    goodCaseContext.getInput().getGoodId(), goodCaseContext.getInput().getGoodUrl());
            existCheckResult.setEnd(true);
            return true;
        }
    }

    public ExistCheckResult process(WorkflowProcess workflowProcess, GoodCaseContext goodCaseContext) {
        log.info("Start exist check, id:{} url:{}", goodCaseContext.getInput().getGoodId(), goodCaseContext.getInput().getGoodUrl());
        ExistCheckResult existCheckResult = new ExistCheckResult();
        String id = goodCaseContext.getInput().getGoodId();
        String url = goodCaseContext.getInput().getGoodUrl();
        //实际执行的节点列表
        List<String> executedNodeIds = goodCaseContext.getTraceLogs().stream().map(TraceLogItem::getNodeId).toList();

        // 获取索引库桶编码
        Set<String> indexCodes = WorkflowUtil.findIndexCodes(workflowProcess, executedNodeIds);
        log.info("indexCodes:{}", indexCodes);

        // 根据索引库桶编码获取关联的数据集桶，根据数据集桶获取源数据桶
        List<AssetBucketDTO> bucketList = this.assetPortalApi.findAllSourceBuckets(indexCodes.stream().findFirst().orElseThrow(() -> new PandoraException("No index codes found.")));

        // 进行存在性检查
        process(existCheckResult, bucketList, goodCaseContext);

        if (StringUtils.isNotBlank(url) && goodCaseContext.getDoc() == null) {
            // 平台没有存储，去dataapi获取
            Optional<PreEvalDoc> crawledDoc = urlDocConverter.fetchDoc(url);
            goodCaseContext.setDoc(JSONObject.from(crawledDoc.orElse(null)));
        }

        log.info("End exist check, id:{} url:{} result:{}",
                goodCaseContext.getInput().getGoodId(),
                goodCaseContext.getInput().getGoodUrl(),
                log.isDebugEnabled() ? existCheckResult.getResult() : "");
        return existCheckResult;
    }

    private void process(ExistCheckResult existCheckResult, List<AssetBucketDTO> bucketList, GoodCaseContext goodCaseContext) {
        //处理顺序：索引库->数据集->站点、文档
        existCheck4Idx(existCheckResult, goodCaseContext, group(bucketList, AssetKindCode.IDX));

        if (existCheckResult.isEnd()) {
            return;
        }
        existCheck4Set(existCheckResult, goodCaseContext, group(bucketList, AssetKindCode.SET));

        if (existCheckResult.isEnd()) {
            return;
        }

        List<AssetBucketDTO> webAndDocBucketDTOs = new ArrayList<>();
        webAndDocBucketDTOs.addAll(group(bucketList, AssetKindCode.WEB));
        webAndDocBucketDTOs.addAll(group(bucketList, AssetKindCode.DOC));

        existCheck4Raw(existCheckResult, goodCaseContext, webAndDocBucketDTOs);
    }

    private void existCheck4Idx(ExistCheckResult existCheckResult, GoodCaseContext goodCaseContext, List<AssetBucketDTO> bucketDTOList4Idx) {
        AssetCellClassifier classifier = new AssetCellClassifier();

        // 收集所有找到的单元并分类
        for (AssetBucketDTO bucket : bucketDTOList4Idx) {
            Optional<AssetCell> cellOptional = getAssetCell(
                    goodCaseContext.getInput().getGoodId(),
                    goodCaseContext.getInput().getGoodUrl(),
                    goodCaseContext.getInput().getRegion(),
                    bucket
            );
            if (cellOptional.isPresent()) {
                classifier.addCell(cellOptional.get(), bucket, this);
            }
        }

        if (!classifier.isEmpty()) {
            goodCaseContext.setDoc(classifier.getFoundCells().getFirst());

            // 使用通用方法检查状态，索引库需要检查黑名单，不需要检查去重
            checkStatusAndSetResult(existCheckResult, goodCaseContext, classifier, "索引库", true, false);
        }
    }

    private void existCheck4Set(ExistCheckResult existCheckResult, GoodCaseContext goodCaseContext, List<AssetBucketDTO> bucketDTOList4Set) {
        AssetCellClassifier classifier = new AssetCellClassifier();

        // 收集所有找到的单元并分类
        for (AssetBucketDTO bucket : bucketDTOList4Set) {
            Optional<AssetCell> cellOptional = getAssetCell(
                    goodCaseContext.getInput().getGoodId(),
                    goodCaseContext.getInput().getGoodUrl(),
                    goodCaseContext.getInput().getRegion(),
                    bucket
            );
            cellOptional.ifPresent(assetCell -> classifier.addCell(assetCell, bucket, this));
        }

        // 记录数据集中找到的记录，用于后续判断是否被数据集规则过滤掉
        existCheckResult.setFoundCellsFromSet(classifier.getFoundCells());

        if (!classifier.isEmpty()) {
            if (goodCaseContext.getDoc() == null) {
                goodCaseContext.setDoc(classifier.getFoundCells().getFirst());
            }

            // 使用通用方法检查状态，数据集不需要检查黑名单，需要检查去重
            checkStatusAndSetResult(existCheckResult, goodCaseContext, classifier, "数据集", false, true);
        }
    }

    private void existCheck4Raw(ExistCheckResult existCheckResult, GoodCaseContext
            goodCaseContext, List<AssetBucketDTO> webAndDocBucketDTOs) {
        AssetCellClassifier classifier = new AssetCellClassifier();

        // 收集所有找到的单元并分类
        for (AssetBucketDTO bucket : webAndDocBucketDTOs) {
            Optional<AssetCell> cellOptional = getAssetCell(
                    goodCaseContext.getInput().getGoodId(),
                    goodCaseContext.getInput().getGoodUrl(),
                    goodCaseContext.getInput().getRegion(),
                    bucket
            );
            cellOptional.ifPresent(assetCell -> classifier.addCell(assetCell, bucket, this));
        }

        if (!classifier.isEmpty()) {
            if (goodCaseContext.getDoc() == null) {
                goodCaseContext.setDoc(classifier.getFoundCells().getFirst());
            }

            // 检查各种状态
            if (classifier.allMatch(classifier.getRejectCells())) {
                existCheckResult.setReason4Rejected();
                existCheckResult.setEnd(true);
                return;
            }

            if (classifier.allMatch(classifier.getUnreviewedCells())) {
                existCheckResult.setReason4Unreviewed();
                existCheckResult.setEnd(true);
                return;
            }

            if (classifier.allMatch(classifier.getDeletedCells())) {
                existCheckResult.setReason4Deleted();
                existCheckResult.setEnd(true);
                return;
            }

            List<AssetCell> statusOkCells = classifier.getStatusOkCells();
            if (!statusOkCells.isEmpty()) {
                log.info("doc在源数据中存在状态正常记录, id:{}, url:{}",
                        goodCaseContext.getInput().getGoodId(), goodCaseContext.getInput().getGoodUrl());
                // 判断是否被源数据 构建 数据集的流程中被规则过滤掉
                if (existCheckResult.getFoundCellsFromSet().isEmpty()) {
                    existCheckResult.setReason4Filtered();
                }
                existCheckResult.setEnd(true);
            } else {
                existCheckResult.setReason4Useless("源数据");
                log.error("doc在源数据中被多种原因过滤掉, id:{}, url:{}",
                        goodCaseContext.getInput().getGoodId(), goodCaseContext.getInput().getGoodUrl());
                existCheckResult.setEnd(true);
            }
        } else {
            // 站点和文档都没有命中，说明未爬取
            existCheckResult.setReason4UnCrawled();
            existCheckResult.setEnd(true);
        }
    }

    /**
     * 判断文档是否在黑名单中
     * 1.文档domain在黑名单中，视为被黑名单过滤
     *
     * @param bucket
     * @param assetCell
     * @return
     */
    private boolean isInBlackList(AssetBucketDTO bucket, AssetCell assetCell) {
        IdxBucketExtConfig extConfig = bucket.getExtConfig().to(IdxBucketExtConfig.class);
        List<String> domainList = domainBlackApi.getDomainList(extConfig.getRegion(), bucket.getCode());
        // 计算被禁用的文档数量
        String domain = assetCell.getString("domain");

        return domain != null && domainList.contains(domain);
    }

    /**
     * 获取AssetCell的属性，如果不存在则返回null
     */
    private AssetCellProps getCellProps(AssetCell assetCell) {
        return assetCell.getPropsOptional().orElse(null);
    }

    /**
     * 判断文档是否未审核
     */
    private boolean isAssetCellUnreviewed(AssetCell assetCell) {
        AssetCellProps props = getCellProps(assetCell);
        return props != null && AssetAuditStatus.PRE_INIT.equals(props.getAuditStatus());
    }

    /**
     * 判断文档是否被拒绝
     */
    private boolean isAssetCellRejected(AssetCell assetCell) {
        AssetCellProps props = getCellProps(assetCell);
        return props != null && AssetAuditStatus.REJECT.equals(props.getAuditStatus());
    }

    /**
     * 判断文档是否被删除
     */
    private boolean isAssetCellDeleted(AssetCell assetCell) {
        AssetCellProps props = getCellProps(assetCell);
        return props != null && AssetOperationType.DELETE.getValue() == props.getIntValue("op", 0);
    }

    /**
     * 判断文档是否被去重
     */
    private boolean isAssetCellDeDuped(AssetCell assetCell) {
        AssetCellProps props = getCellProps(assetCell);
        return props != null && props.getIntValue("s", 0) == 1;
    }

    private Optional<AssetCell> getAssetCell(String id, String url, String region, AssetBucketDTO bucket) {
        // 查询文档
        CellQueryDTO cellQueryDTO = new CellQueryDTO();
        cellQueryDTO.setRegionCode(region);
        cellQueryDTO.setBucketCode(bucket.getCode());
        if (StringUtils.isNotBlank(id)) {
            cellQueryDTO.setIds(Collections.singletonList(id));
        }
        if (StringUtils.isNotBlank(url)) {
            cellQueryDTO.setDsl(buildDsl("url", url));
        }
        List<AssetCell> docs = this.assetPortalApi.listAssetCell(cellQueryDTO);
        if (CollectionUtils.isEmpty(docs)) {
            return Optional.empty();
        }
        return Optional.of(docs.getFirst());
    }

    private List<AssetBucketDTO> group(List<AssetBucketDTO> bucketList, String kindCode) {
        return bucketList.stream()
                .filter(bucket -> kindCode.equals(bucket.getKindCode()))
                .toList();
    }

    private String buildDsl(String key, String value) {
        return "{\"@type\":\"AND\",\"nodes\":[{\"@type\":\"STRING\",\"op\":\"EQ\",\"field\":\"%s\",\"args\":[\"%s\"]}]}".formatted(key, value);
    }
}
