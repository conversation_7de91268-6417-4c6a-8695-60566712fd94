### 预览数据单元
POST {{baseUrl}}/asset/api/v1/cell/list
Content-Type: application/json

{
  "bucketCode": "your_bucket_code",
  "filters": {}
}

### 查询数据单元详情
GET {{baseUrl}}/asset/api/v1/cell/detail?bucketCode=your_bucket_code&id=your_cell_id

### 文件上传示例
POST {{baseUrl}}/asset/api/v1/cell/upload
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="bucketCode"

your_bucket_code
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="files"; filename="example.txt"
Content-Type: text/plain

< ./example.txt
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 文件下载示例
GET {{baseUrl}}/asset/api/v1/cell/download/your_bucket_code/your_file_id

### 导出数据
POST {{baseUrl}}/asset/api/v1/cell/export
Content-Type: application/json

{
  "bucketCode": "your_bucket_code",
  "query": {
    "filters": {}
  }
}